#!/usr/bin/env python3
"""
TIF ve KML dosyalarını analiz eden script.
TIF dosyasındaki görüntüyü KML dosyasındaki sınırlarla üst üste örtüp analiz eder.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import warnings
warnings.filterwarnings('ignore')

def check_and_install_packages():
    """Gerekli paketlerin yüklü olup olmadığını kontrol eder ve gerekirse yükler."""
    required_packages = {
        'rasterio': 'rasterio',
        'geopandas': 'geopandas', 
        'shapely': 'shapely',
        'fiona': 'fiona'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name} yüklü")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} eksik")
    
    if missing_packages:
        print(f"\nEksik paketler yükleniyor: {', '.join(missing_packages)}")
        for package in missing_packages:
            os.system(f"pip install {package}")
        print("Paket yükleme tamamlandı.\n")

def analyze_tif_file(tif_path):
    """TIF dosyasını analiz eder."""
    import rasterio
    from rasterio.plot import show

    print(f"TIF dosyası analiz ediliyor: {tif_path}")

    src = rasterio.open(tif_path)
    print(f"  - Boyutlar: {src.width} x {src.height}")
    print(f"  - Band sayısı: {src.count}")
    print(f"  - Koordinat sistemi: {src.crs}")
    print(f"  - Sınırlar: {src.bounds}")
    print(f"  - Transform: {src.transform}")

    # Dosya çok büyükse küçük bir çözünürlükle oku
    max_size = 5000  # Maksimum boyut
    if src.width > max_size or src.height > max_size:
        # Ölçekleme faktörünü hesapla
        scale_factor = max(src.width / max_size, src.height / max_size)
        new_width = int(src.width / scale_factor)
        new_height = int(src.height / scale_factor)

        print(f"  - Dosya çok büyük, küçültülüyor: {new_width} x {new_height}")
        print(f"  - Ölçekleme faktörü: {scale_factor:.2f}")

        # Küçültülmüş veriyi oku
        data = src.read(1, out_shape=(new_height, new_width), resampling=rasterio.enums.Resampling.average)

        # Transform'u güncelle
        transform = src.transform * src.transform.scale(
            (src.width / new_width),
            (src.height / new_height)
        )
    else:
        # Normal boyutta oku
        data = src.read(1)
        transform = src.transform

    print(f"  - Okunan veri boyutu: {data.shape}")
    print(f"  - Veri tipi: {data.dtype}")
    print(f"  - Min değer: {np.min(data)}")
    print(f"  - Max değer: {np.max(data)}")
    print(f"  - Benzersiz değer sayısı: {len(np.unique(data))}")

    return src, data, transform

def analyze_kml_file(kml_path):
    """KML dosyasını analiz eder."""
    import geopandas as gpd
    import fiona
    
    print(f"\nKML dosyası analiz ediliyor: {kml_path}")
    
    # KML dosyasındaki katmanları listele
    layers = fiona.listlayers(kml_path)
    print(f"  - Katman sayısı: {len(layers)}")
    for i, layer in enumerate(layers):
        print(f"    {i+1}. {layer}")
    
    # Her katmanı oku
    gdfs = {}
    for layer in layers:
        try:
            gdf = gpd.read_file(kml_path, layer=layer)
            gdfs[layer] = gdf
            print(f"\n  Katman: {layer}")
            print(f"    - Özellik sayısı: {len(gdf)}")
            print(f"    - Geometri tipi: {gdf.geometry.geom_type.unique()}")
            print(f"    - Koordinat sistemi: {gdf.crs}")
            print(f"    - Sınırlar: {gdf.total_bounds}")
            
            # İlk birkaç özelliği göster
            if len(gdf) > 0:
                print(f"    - Sütunlar: {list(gdf.columns)}")
                if 'Name' in gdf.columns:
                    print(f"    - İlk 3 isim: {gdf['Name'].head(3).tolist()}")
        except Exception as e:
            print(f"    - Hata: {e}")
    
    return gdfs

def overlay_and_visualize(tif_src, tif_data, tif_transform, kml_gdfs):
    """TIF ve KML verilerini üst üste örtüp görselleştir."""
    import rasterio
    from rasterio.plot import show
    import geopandas as gpd

    print("\nGörselleştirme oluşturuluyor...")

    # Figure oluştur
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('TIF ve KML Dosyalarının Analizi', fontsize=16)

    # 1. TIF dosyasını göster (küçültülmüş veri ile)
    ax1 = axes[0, 0]
    ax1.imshow(tif_data, extent=[tif_src.bounds.left, tif_src.bounds.right,
                                tif_src.bounds.bottom, tif_src.bounds.top],
               cmap='gray', aspect='auto')
    ax1.set_title('TIF Dosyası (Gri Tonlama)')
    ax1.set_xlabel('X Koordinatı')
    ax1.set_ylabel('Y Koordinatı')

    # 2. TIF dosyasını renkli göster
    ax2 = axes[0, 1]
    im = ax2.imshow(tif_data, extent=[tif_src.bounds.left, tif_src.bounds.right,
                                     tif_src.bounds.bottom, tif_src.bounds.top],
                    cmap='viridis', aspect='auto')
    ax2.set_title('TIF Dosyası (Renkli)')
    ax2.set_xlabel('X Koordinatı')
    ax2.set_ylabel('Y Koordinatı')
    plt.colorbar(im, ax=ax2)
    
    # 3. KML geometrilerini göster
    ax3 = axes[1, 0]
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    
    for i, (layer_name, gdf) in enumerate(kml_gdfs.items()):
        if len(gdf) > 0:
            # Koordinat sistemini TIF ile uyumlu hale getir
            if gdf.crs != tif_src.crs:
                gdf = gdf.to_crs(tif_src.crs)
            
            color = colors[i % len(colors)]
            if 'Point' in gdf.geometry.geom_type.iloc[0]:
                gdf.plot(ax=ax3, color=color, markersize=1, alpha=0.7, label=f'{layer_name} (Points)')
            else:
                gdf.plot(ax=ax3, color=color, alpha=0.3, edgecolor=color, linewidth=0.5, label=f'{layer_name} (Polygons)')
    
    ax3.set_title('KML Geometrileri')
    ax3.set_xlabel('X Koordinatı')
    ax3.set_ylabel('Y Koordinatı')
    ax3.legend()
    
    # 4. TIF ve KML'i üst üste örtüp göster
    ax4 = axes[1, 1]
    
    # TIF'i arka plan olarak göster
    ax4.imshow(tif_data, extent=[tif_src.bounds.left, tif_src.bounds.right, 
                                tif_src.bounds.bottom, tif_src.bounds.top],
               cmap='gray', alpha=0.7, aspect='auto')
    
    # KML geometrilerini üzerine çiz
    for i, (layer_name, gdf) in enumerate(kml_gdfs.items()):
        if len(gdf) > 0:
            if gdf.crs != tif_src.crs:
                gdf = gdf.to_crs(tif_src.crs)
            
            color = colors[i % len(colors)]
            if 'Point' in gdf.geometry.geom_type.iloc[0]:
                gdf.plot(ax=ax4, color=color, markersize=2, alpha=0.8)
            else:
                gdf.plot(ax=ax4, facecolor='none', edgecolor=color, linewidth=1, alpha=0.8)
    
    ax4.set_title('TIF + KML Üst Üste')
    ax4.set_xlabel('X Koordinatı')
    ax4.set_ylabel('Y Koordinatı')
    
    plt.tight_layout()
    plt.savefig('tif_kml_analysis.png', dpi=300, bbox_inches='tight')
    print("Görselleştirme 'tif_kml_analysis.png' olarak kaydedildi.")
    plt.show()

def find_overlapping_areas(tif_src, tif_data, tif_transform, kml_gdfs):
    """TIF ve KML arasındaki örtüşen alanları bul."""
    import geopandas as gpd
    from shapely.geometry import box
    import rasterio
    from rasterio.features import geometry_mask

    print("\nÖrtüşen alanlar analiz ediliyor...")

    # TIF sınırlarını al
    tif_bounds = tif_src.bounds
    tif_box = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)

    results = {}

    for layer_name, gdf in kml_gdfs.items():
        if len(gdf) == 0:
            continue

        # Koordinat sistemini uyumlu hale getir
        if gdf.crs != tif_src.crs:
            gdf = gdf.to_crs(tif_src.crs)

        # Polygon geometrileri için örtüşme analizi
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.intersects(tif_box)]

            if len(overlapping) > 0:
                print(f"\n{layer_name} katmanında {len(overlapping)} örtüşen polygon bulundu:")

                for idx, row in overlapping.iterrows():
                    if 'Name' in row:
                        print(f"  - {row['Name']}")
                    else:
                        print(f"  - Polygon {idx}")

                # Mask oluştur (küçültülmüş transform ile)
                try:
                    mask = geometry_mask(overlapping.geometry,
                                       transform=tif_transform,
                                       invert=True,
                                       out_shape=tif_data.shape)

                    # Örtüşen alandaki TIF değerlerini analiz et
                    masked_data = tif_data[mask]
                    if len(masked_data) > 0:
                        print(f"    Örtüşen alandaki TIF değerleri:")
                        print(f"      - Min: {np.min(masked_data)}")
                        print(f"      - Max: {np.max(masked_data)}")
                        print(f"      - Ortalama: {np.mean(masked_data):.2f}")
                        print(f"      - Benzersiz değerler: {np.unique(masked_data)}")

                        results[layer_name] = {
                            'overlapping_count': len(overlapping),
                            'overlapping_polygons': overlapping,
                            'mask': mask,
                            'tif_values': masked_data
                        }
                except Exception as e:
                    print(f"    Mask oluşturma hatası: {e}")

        # Point geometrileri için örtüşme analizi
        elif 'Point' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.within(tif_box)]

            if len(overlapping) > 0:
                print(f"\n{layer_name} katmanında {len(overlapping)} örtüşen nokta bulundu")
                results[layer_name] = {
                    'overlapping_count': len(overlapping),
                    'overlapping_points': overlapping
                }

    return results

def main():
    """Ana fonksiyon."""
    print("TIF ve KML Dosya Analizi")
    print("=" * 50)

    # Dosya yolları
    tif_path = "demirozu_with_model_output.tif"
    kml_path = "Layers.kml"

    # Dosyaların varlığını kontrol et
    if not os.path.exists(tif_path):
        print(f"Hata: {tif_path} dosyası bulunamadı!")
        return

    if not os.path.exists(kml_path):
        print(f"Hata: {kml_path} dosyası bulunamadı!")
        return

    # Gerekli paketleri kontrol et ve yükle
    check_and_install_packages()

    tif_src = None
    try:
        # TIF dosyasını analiz et
        tif_src, tif_data, tif_transform = analyze_tif_file(tif_path)

        # KML dosyasını analiz et
        kml_gdfs = analyze_kml_file(kml_path)

        # Görselleştir
        overlay_and_visualize(tif_src, tif_data, tif_transform, kml_gdfs)

        # Örtüşen alanları bul
        overlapping_results = find_overlapping_areas(tif_src, tif_data, tif_transform, kml_gdfs)

        print("\n" + "=" * 50)
        print("Analiz tamamlandı!")
        print("Sonuçlar 'tif_kml_analysis.png' dosyasında görselleştirildi.")

    except Exception as e:
        print(f"Hata oluştu: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # TIF dosyasını kapat
        if tif_src is not None:
            tif_src.close()

if __name__ == "__main__":
    main()
