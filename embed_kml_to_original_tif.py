#!/usr/bin/env python3
"""
KML dosyasındaki parsel sınırlarını orijinal TIF dosyasına tam boyutunda işler.
Window-based processing kullanarak bellek dostu şekilde çalışır.
"""

import os
import numpy as np
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def check_and_install_packages():
    """Gerekli paketlerin yüklü olup olmadığını kontrol eder."""
    required_packages = {
        'rasterio': 'rasterio',
        'geopandas': 'geopandas', 
        'shapely': 'shapely',
        'fiona': 'fiona',
        'tqdm': 'tqdm'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name} yüklü")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} eksik")
    
    if missing_packages:
        print(f"\nEksik paketler yükleniyor: {', '.join(missing_packages)}")
        for package in missing_packages:
            os.system(f"pip install {package}")
        print("Paket yükleme tamamlandı.\n")

def load_kml_data(kml_path, target_crs):
    """KML dosyasını yükler ve hedef koordinat sistemine çevirir."""
    import geopandas as gpd
    import fiona
    
    print("KML verisi yükleniyor...")
    
    layers = fiona.listlayers(kml_path)
    kml_gdfs = {}
    
    for layer in layers:
        try:
            gdf = gpd.read_file(kml_path, layer=layer)
            if len(gdf) > 0:
                # Koordinat sistemini hedef CRS'e çevir
                if gdf.crs != target_crs:
                    gdf = gdf.to_crs(target_crs)
                kml_gdfs[layer] = gdf
                print(f"✓ {layer}: {len(gdf)} özellik yüklendi")
        except Exception as e:
            print(f"✗ {layer} yükleme hatası: {e}")
    
    return kml_gdfs

def find_overlapping_features(tif_bounds, kml_gdfs):
    """TIF ile örtüşen KML özelliklerini bul."""
    from shapely.geometry import box
    
    print("\nÖrtüşen özellikler bulunuyor...")
    
    tif_box = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)
    overlapping_features = {}
    
    for layer_name, gdf in kml_gdfs.items():
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.intersects(tif_box)]
            if len(overlapping) > 0:
                overlapping_features[layer_name] = overlapping
                print(f"✓ {layer_name}: {len(overlapping)} örtüşen polygon")
        elif 'Point' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.within(tif_box)]
            if len(overlapping) > 0:
                overlapping_features[layer_name] = overlapping
                print(f"✓ {layer_name}: {len(overlapping)} örtüşen nokta")
    
    return overlapping_features

def create_parcel_masks(overlapping_features, transform, shape):
    """Parsel geometrilerinden mask'lar oluştur."""
    from rasterio.features import rasterize
    
    print("\nParsel mask'ları oluşturuluyor...")
    
    masks = {}
    
    for layer_name, gdf in overlapping_features.items():
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            print(f"  {layer_name} mask'ı oluşturuluyor...")
            
            # Geometrileri rasterize et
            shapes = [(geom, 1) for geom in gdf.geometry]
            
            try:
                mask = rasterize(
                    shapes,
                    out_shape=shape,
                    transform=transform,
                    fill=0,
                    dtype=np.uint8
                )
                masks[layer_name] = mask
                print(f"  ✓ {layer_name} mask'ı hazır")
                
            except Exception as e:
                print(f"  ✗ {layer_name} mask hatası: {e}")
    
    return masks

def process_tif_with_windows(input_path, output_path, masks, chunk_size=1024):
    """TIF dosyasını window-based processing ile işle."""
    import rasterio
    from rasterio.windows import Window
    
    print(f"\nOrijinal TIF işleniyor (chunk size: {chunk_size}x{chunk_size})...")
    
    with rasterio.open(input_path) as src:
        # Çıktı dosyasının meta verilerini hazırla
        profile = src.profile.copy()
        profile.update({
            'compress': 'lzw',
            'tiled': True,
            'blockxsize': 512,
            'blockysize': 512
        })
        
        print(f"Kaynak dosya: {src.width}x{src.height}, {src.count} band")
        print(f"Hedef dosya: {output_path}")
        
        # Toplam window sayısını hesapla
        total_windows = 0
        for row in range(0, src.height, chunk_size):
            for col in range(0, src.width, chunk_size):
                total_windows += 1
        
        print(f"Toplam {total_windows} window işlenecek...")
        
        # Çıktı dosyasını oluştur
        with rasterio.open(output_path, 'w', **profile) as dst:
            
            # Progress bar ile window'ları işle
            with tqdm(total=total_windows, desc="TIF İşleniyor") as pbar:
                
                for row in range(0, src.height, chunk_size):
                    for col in range(0, src.width, chunk_size):
                        
                        # Window boyutlarını hesapla
                        window_height = min(chunk_size, src.height - row)
                        window_width = min(chunk_size, src.width - col)
                        
                        window = Window(col, row, window_width, window_height)
                        
                        # Bu window için veriyi oku
                        data = src.read(window=window)
                        
                        # Mask'ları bu window için uygula
                        for layer_name, mask in masks.items():
                            # Mask'ın bu window kısmını al
                            mask_window = mask[row:row+window_height, col:col+window_width]
                            
                            # Parsel sınırlarını vurgula (kırmızı çizgi)
                            if mask_window.any():
                                # Sınır piksellerini bul (edge detection)
                                from scipy import ndimage
                                edges = ndimage.binary_dilation(mask_window) ^ mask_window
                                
                                # Kırmızı çizgi ekle
                                if data.shape[0] >= 3:  # RGB varsa
                                    data[0][edges] = 255  # Red channel
                                    data[1][edges] = 0    # Green channel  
                                    data[2][edges] = 0    # Blue channel
                                else:  # Tek band ise
                                    data[0][edges] = 255
                        
                        # İşlenmiş veriyi yaz
                        dst.write(data, window=window)
                        
                        pbar.update(1)
        
        print(f"✓ İşlem tamamlandı: {output_path}")

def add_scipy_if_needed():
    """Scipy paketini kontrol et ve gerekirse yükle."""
    try:
        import scipy
        print("✓ scipy yüklü")
    except ImportError:
        print("scipy yükleniyor...")
        os.system("pip install scipy")
        print("✓ scipy yüklendi")

def main():
    """Ana fonksiyon."""
    print("🗺️  Orijinal Boyutta KML-TIF İşleme Aracı")
    print("=" * 60)
    
    # Dosya yolları
    input_tif = "demirozu_with_model_output.tif"
    kml_path = "Layers.kml"
    output_tif = "demirozu_with_parcels_FULL_RESOLUTION.tif"
    
    # Dosyaların varlığını kontrol et
    if not os.path.exists(input_tif):
        print(f"❌ Hata: {input_tif} dosyası bulunamadı!")
        return
    
    if not os.path.exists(kml_path):
        print(f"❌ Hata: {kml_path} dosyası bulunamadı!")
        return
    
    # Gerekli paketleri kontrol et
    check_and_install_packages()
    add_scipy_if_needed()
    
    start_time = time.time()
    
    try:
        import rasterio
        
        # TIF dosyasının bilgilerini al
        with rasterio.open(input_tif) as src:
            print(f"\n📊 TIF Dosya Bilgileri:")
            print(f"   Boyutlar: {src.width:,} x {src.height:,} piksel")
            print(f"   Band sayısı: {src.count}")
            print(f"   Koordinat sistemi: {src.crs}")
            print(f"   Dosya boyutu: ~{os.path.getsize(input_tif) / (1024**3):.1f} GB")
            
            # KML verilerini yükle
            kml_gdfs = load_kml_data(kml_path, src.crs)
            
            # Örtüşen özellikleri bul
            overlapping_features = find_overlapping_features(src.bounds, kml_gdfs)
            
            if not overlapping_features:
                print("❌ Örtüşen özellik bulunamadı!")
                return
            
            # Parsel mask'larını oluştur
            masks = create_parcel_masks(overlapping_features, src.transform, 
                                      (src.height, src.width))
            
            if not masks:
                print("❌ Mask oluşturulamadı!")
                return
        
        # Chunk size'ı bellek durumuna göre ayarla
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        
        if available_memory_gb > 16:
            chunk_size = 2048
        elif available_memory_gb > 8:
            chunk_size = 1024
        else:
            chunk_size = 512
            
        print(f"\n💾 Kullanılabilir RAM: {available_memory_gb:.1f} GB")
        print(f"📦 Chunk boyutu: {chunk_size}x{chunk_size}")
        
        # Ana işlemi başlat
        process_tif_with_windows(input_tif, output_tif, masks, chunk_size)
        
        # Sonuçları göster
        elapsed_time = time.time() - start_time
        output_size = os.path.getsize(output_tif) / (1024**3)
        
        print(f"\n🎉 İşlem Başarıyla Tamamlandı!")
        print(f"⏱️  Süre: {elapsed_time/60:.1f} dakika")
        print(f"📁 Çıktı dosyası: {output_tif}")
        print(f"📊 Çıktı boyutu: {output_size:.1f} GB")
        print(f"🔍 Çözünürlük: TAM BOYUT (küçültme yok)")
        print(f"\n✨ Artık {output_tif} dosyasını açtığınızda")
        print(f"   parsel sınırları orijinal çözünürlükte görünecek!")
        
    except Exception as e:
        print(f"❌ Hata oluştu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
