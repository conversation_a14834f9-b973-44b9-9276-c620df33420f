#!/usr/bin/env python3
"""
KML-TIF eşleştirmesinin doğruluğunu kontrol eder.
Koordinat dönüşümlerini test eder ve görsel verification sağlar.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def check_packages():
    """Gerekli paketleri kontrol et."""
    try:
        import rasterio
        import geopandas as gpd
        import fiona
        from shapely.geometry import Point, box
        print("✓ Tüm paketler yüklü")
        return True
    except ImportError as e:
        print(f"✗ Paket eksik: {e}")
        return False

def load_data():
    """TIF ve KML verilerini yükle."""
    import rasterio
    import geopandas as gpd
    import fiona
    
    print("📊 Veri yükleniyor...")
    
    # TIF dosyasını yükle
    tif_path = "demirozu_with_model_output.tif"
    tif_src = rasterio.open(tif_path)
    
    print(f"TIF Bilgileri:")
    print(f"  - Boyutlar: {tif_src.width} x {tif_src.height}")
    print(f"  - CRS: {tif_src.crs}")
    print(f"  - Bounds: {tif_src.bounds}")
    
    # KML dosyasını yükle
    kml_path = "Layers.kml"
    layers = fiona.listlayers(kml_path)
    
    kml_data = {}
    for layer in layers:
        gdf = gpd.read_file(kml_path, layer=layer)
        kml_data[layer] = gdf
        print(f"\nKML Katmanı: {layer}")
        print(f"  - Özellik sayısı: {len(gdf)}")
        print(f"  - CRS: {gdf.crs}")
        print(f"  - Bounds: {gdf.total_bounds}")
    
    return tif_src, kml_data

def test_coordinate_transformation():
    """Koordinat dönüşümünü test et."""
    import geopandas as gpd
    from shapely.geometry import Point
    
    print("\n🔍 Koordinat Dönüşüm Testi")
    print("=" * 50)
    
    # Test noktaları (KML'den alınan gerçek koordinatlar)
    test_points = [
        (39.84507937399471, 40.2156027378709),  # İlk parsel
        (39.86100882399384, 40.20937692840043),  # Orta parsel
        (39.89057106506614, 40.2220629072841),   # Son parsel
    ]
    
    print("Test Noktaları (WGS84):")
    for i, (lon, lat) in enumerate(test_points):
        print(f"  {i+1}. Lon: {lon:.6f}, Lat: {lat:.6f}")
    
    # GeoPandas ile dönüşüm
    gdf_test = gpd.GeoDataFrame(
        geometry=[Point(lon, lat) for lon, lat in test_points],
        crs='EPSG:4326'
    )
    
    # EPSG:5257'ye dönüştür
    gdf_transformed = gdf_test.to_crs('EPSG:5257')
    
    print("\nDönüştürülmüş Koordinatlar (EPSG:5257):")
    for i, geom in enumerate(gdf_transformed.geometry):
        print(f"  {i+1}. X: {geom.x:.2f}, Y: {geom.y:.2f}")
    
    # Geri dönüşüm testi
    gdf_back = gdf_transformed.to_crs('EPSG:4326')
    
    print("\nGeri Dönüşüm Testi (Hata Analizi):")
    for i, (original, back) in enumerate(zip(test_points, gdf_back.geometry)):
        lon_error = abs(original[0] - back.x)
        lat_error = abs(original[1] - back.y)
        print(f"  {i+1}. Lon hatası: {lon_error:.8f}°, Lat hatası: {lat_error:.8f}°")
        
        # Metre cinsinden hata (yaklaşık)
        lon_error_m = lon_error * 111320 * np.cos(np.radians(original[1]))
        lat_error_m = lat_error * 110540
        print(f"      Yaklaşık hata: {lon_error_m:.3f}m (X), {lat_error_m:.3f}m (Y)")
    
    return gdf_test, gdf_transformed

def analyze_overlap_accuracy(tif_src, kml_data):
    """Örtüşme doğruluğunu analiz et."""
    from shapely.geometry import box
    
    print("\n🎯 Örtüşme Doğruluk Analizi")
    print("=" * 50)
    
    # TIF sınırları
    tif_bounds = tif_src.bounds
    tif_box = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)
    
    print(f"TIF Sınırları (EPSG:5257):")
    print(f"  Left: {tif_bounds.left:.2f}")
    print(f"  Bottom: {tif_bounds.bottom:.2f}")
    print(f"  Right: {tif_bounds.right:.2f}")
    print(f"  Top: {tif_bounds.top:.2f}")
    
    results = {}
    
    for layer_name, gdf in kml_data.items():
        print(f"\n📍 Katman: {layer_name}")
        
        # Orijinal CRS'de bounds
        orig_bounds = gdf.total_bounds
        print(f"  Orijinal bounds (EPSG:4326): {orig_bounds}")
        
        # Dönüştürülmüş CRS'de bounds
        gdf_transformed = gdf.to_crs(tif_src.crs)
        trans_bounds = gdf_transformed.total_bounds
        print(f"  Dönüştürülmüş bounds (EPSG:5257): {trans_bounds}")
        
        # Örtüşme analizi
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf_transformed[gdf_transformed.geometry.intersects(tif_box)]
            total_in_bounds = gdf_transformed[gdf_transformed.geometry.within(tif_box)]
            
            print(f"  Toplam polygon: {len(gdf_transformed)}")
            print(f"  Örtüşen polygon: {len(overlapping)}")
            print(f"  Tamamen içerde: {len(total_in_bounds)}")
            print(f"  Örtüşme oranı: {len(overlapping)/len(gdf_transformed)*100:.1f}%")
            
        elif 'Point' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf_transformed[gdf_transformed.geometry.within(tif_box)]
            
            print(f"  Toplam nokta: {len(gdf_transformed)}")
            print(f"  İçerideki nokta: {len(overlapping)}")
            print(f"  İçeride oranı: {len(overlapping)/len(gdf_transformed)*100:.1f}%")
        
        results[layer_name] = {
            'original': gdf,
            'transformed': gdf_transformed,
            'overlapping': overlapping if 'overlapping' in locals() else None
        }
    
    return results

def create_visual_verification(tif_src, analysis_results):
    """Görsel doğrulama oluştur."""
    from shapely.geometry import box
    
    print("\n📊 Görsel Doğrulama Oluşturuluyor...")
    
    # Figure oluştur
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('KML-TIF Eşleştirme Doğruluk Analizi', fontsize=16)
    
    # TIF sınırları
    tif_bounds = tif_src.bounds
    tif_box = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)
    
    # 1. Orijinal KML (WGS84)
    ax1 = axes[0, 0]
    for layer_name, data in analysis_results.items():
        gdf = data['original']
        if 'Point' in gdf.geometry.geom_type.iloc[0]:
            gdf.plot(ax=ax1, color='red', markersize=1, alpha=0.7, label=layer_name)
        else:
            gdf.plot(ax=ax1, color='blue', alpha=0.3, edgecolor='blue', linewidth=0.5, label=layer_name)
    
    ax1.set_title('Orijinal KML (EPSG:4326)')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Dönüştürülmüş KML (EPSG:5257)
    ax2 = axes[0, 1]
    for layer_name, data in analysis_results.items():
        gdf = data['transformed']
        if 'Point' in gdf.geometry.geom_type.iloc[0]:
            gdf.plot(ax=ax2, color='red', markersize=1, alpha=0.7, label=layer_name)
        else:
            gdf.plot(ax=ax2, color='blue', alpha=0.3, edgecolor='blue', linewidth=0.5, label=layer_name)
    
    # TIF sınırlarını ekle
    x_coords = [tif_bounds.left, tif_bounds.right, tif_bounds.right, tif_bounds.left, tif_bounds.left]
    y_coords = [tif_bounds.bottom, tif_bounds.bottom, tif_bounds.top, tif_bounds.top, tif_bounds.bottom]
    ax2.plot(x_coords, y_coords, 'k-', linewidth=2, label='TIF Sınırları')
    
    ax2.set_title('Dönüştürülmüş KML + TIF Sınırları (EPSG:5257)')
    ax2.set_xlabel('X (metre)')
    ax2.set_ylabel('Y (metre)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Örtüşme Analizi
    ax3 = axes[1, 0]
    for layer_name, data in analysis_results.items():
        if data['overlapping'] is not None and len(data['overlapping']) > 0:
            gdf = data['overlapping']
            if 'Point' in gdf.geometry.geom_type.iloc[0]:
                gdf.plot(ax=ax3, color='green', markersize=2, alpha=0.8, label=f'{layer_name} (Örtüşen)')
            else:
                gdf.plot(ax=ax3, color='green', alpha=0.5, edgecolor='green', linewidth=1, label=f'{layer_name} (Örtüşen)')
    
    # TIF sınırlarını ekle
    ax3.plot(x_coords, y_coords, 'k-', linewidth=2, label='TIF Sınırları')
    
    ax3.set_title('Örtüşen Özellikler')
    ax3.set_xlabel('X (metre)')
    ax3.set_ylabel('Y (metre)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. İstatistikler
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    stats_text = "📊 Eşleştirme İstatistikleri\n\n"
    
    for layer_name, data in analysis_results.items():
        total = len(data['transformed'])
        overlapping = len(data['overlapping']) if data['overlapping'] is not None else 0
        percentage = (overlapping / total * 100) if total > 0 else 0
        
        stats_text += f"{layer_name}:\n"
        stats_text += f"  • Toplam: {total}\n"
        stats_text += f"  • Örtüşen: {overlapping}\n"
        stats_text += f"  • Oran: {percentage:.1f}%\n\n"
    
    stats_text += "⚠️ Doğruluk Notları:\n"
    stats_text += "• Koordinat dönüşümü 1-5m hata içerebilir\n"
    stats_text += "• Projeksiyon distorsiyonları olabilir\n"
    stats_text += "• Manuel kontrol önerilir\n"
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('matching_accuracy_verification.png', dpi=300, bbox_inches='tight')
    print("✓ Görsel doğrulama kaydedildi: matching_accuracy_verification.png")
    plt.show()

def main():
    """Ana fonksiyon."""
    print("🔍 KML-TIF Eşleştirme Doğruluk Kontrolü")
    print("=" * 60)
    
    if not check_packages():
        return
    
    try:
        # Veriyi yükle
        tif_src, kml_data = load_data()
        
        # Koordinat dönüşümünü test et
        test_points, transformed_points = test_coordinate_transformation()
        
        # Örtüşme doğruluğunu analiz et
        analysis_results = analyze_overlap_accuracy(tif_src, kml_data)
        
        # Görsel doğrulama oluştur
        create_visual_verification(tif_src, analysis_results)
        
        print("\n" + "=" * 60)
        print("🎯 SONUÇ: Eşleştirme Doğruluğu")
        print("=" * 60)
        print("✅ Koordinat dönüşümü çalışıyor")
        print("⚠️  1-5 metre hassasiyet hatası olabilir")
        print("⚠️  Manuel kontrol önerilir")
        print("📊 Detaylı analiz: matching_accuracy_verification.png")
        
        # Öneriler
        print("\n💡 Doğruluğu Artırma Önerileri:")
        print("1. Ground Control Points (GCP) kullanın")
        print("2. Daha hassas koordinat dönüşümü yapın")
        print("3. Manuel sample kontrolleri yapın")
        print("4. Alternatif CRS deneyin")
        
    except Exception as e:
        print(f"❌ Hata: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'tif_src' in locals():
            tif_src.close()

if __name__ == "__main__":
    main()
