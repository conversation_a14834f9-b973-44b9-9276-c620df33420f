#!/usr/bin/env python3
"""
KML dosyasındaki parsel sınırlarını ve bilgilerini TIF dosyasına kalıcı olarak işler.
Sonuç olarak yeni bir TIF dosyası oluşturur ki bu dosyayı açtığınızda parsel bilgileri görünür.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon as MPLPolygon
import warnings
warnings.filterwarnings('ignore')

def check_and_install_packages():
    """Gerekli paketlerin yüklü olup olmadığını kontrol eder."""
    required_packages = {
        'rasterio': 'rasterio',
        'geopandas': 'geopandas', 
        'shapely': 'shapely',
        'fiona': 'fiona',
        'PIL': 'PIL'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name} yüklü")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} eksik")
    
    if missing_packages:
        print(f"\nEksik paketler yükleniyor: {', '.join(missing_packages)}")
        for package in missing_packages:
            if package == 'PIL':
                os.system("pip install Pillow")
            else:
                os.system(f"pip install {package}")
        print("Paket yükleme tamamlandı.\n")

def load_and_prepare_data(tif_path, kml_path):
    """TIF ve KML dosyalarını yükler ve hazırlar."""
    import rasterio
    import geopandas as gpd
    import fiona
    
    print("Veri yükleniyor...")
    
    # TIF dosyasını yükle
    tif_src = rasterio.open(tif_path)
    print(f"TIF boyutları: {tif_src.width} x {tif_src.height}")
    
    # Eğer dosya çok büyükse küçült
    max_size = 8000
    if tif_src.width > max_size or tif_src.height > max_size:
        scale_factor = max(tif_src.width / max_size, tif_src.height / max_size)
        new_width = int(tif_src.width / scale_factor)
        new_height = int(tif_src.height / scale_factor)
        print(f"Dosya küçültülüyor: {new_width} x {new_height}")
        
        # 3 bandı da oku (RGB)
        tif_data = tif_src.read(out_shape=(tif_src.count, new_height, new_width), 
                               resampling=rasterio.enums.Resampling.average)
        
        # Transform'u güncelle
        transform = tif_src.transform * tif_src.transform.scale(
            (tif_src.width / new_width),
            (tif_src.height / new_height)
        )
    else:
        tif_data = tif_src.read()
        transform = tif_src.transform
    
    print(f"Yüklenen TIF boyutu: {tif_data.shape}")
    
    # KML dosyasını yükle
    layers = fiona.listlayers(kml_path)
    kml_gdfs = {}
    
    for layer in layers:
        try:
            gdf = gpd.read_file(kml_path, layer=layer)
            if len(gdf) > 0:
                # Koordinat sistemini TIF ile uyumlu hale getir
                if gdf.crs != tif_src.crs:
                    gdf = gdf.to_crs(tif_src.crs)
                kml_gdfs[layer] = gdf
                print(f"KML katmanı yüklendi: {layer} ({len(gdf)} özellik)")
        except Exception as e:
            print(f"Katman yükleme hatası {layer}: {e}")
    
    return tif_src, tif_data, transform, kml_gdfs

def find_overlapping_features(tif_src, kml_gdfs):
    """TIF ile örtüşen KML özelliklerini bul."""
    from shapely.geometry import box
    
    print("\nÖrtüşen özellikler bulunuyor...")
    
    # TIF sınırlarını al
    tif_bounds = tif_src.bounds
    tif_box = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)
    
    overlapping_features = {}
    
    for layer_name, gdf in kml_gdfs.items():
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.intersects(tif_box)]
            if len(overlapping) > 0:
                overlapping_features[layer_name] = overlapping
                print(f"{layer_name}: {len(overlapping)} örtüşen polygon")
        elif 'Point' in gdf.geometry.geom_type.iloc[0]:
            overlapping = gdf[gdf.geometry.within(tif_box)]
            if len(overlapping) > 0:
                overlapping_features[layer_name] = overlapping
                print(f"{layer_name}: {len(overlapping)} örtüşen nokta")
    
    return overlapping_features

def create_enhanced_tif(tif_data, tif_transform, tif_bounds, overlapping_features, output_path):
    """KML bilgilerini TIF'e işleyerek yeni bir TIF oluştur."""
    import rasterio
    from rasterio.features import rasterize
    from shapely.geometry import Point, Polygon
    import matplotlib.pyplot as plt
    from matplotlib.patches import Polygon as MPLPolygon
    import matplotlib.patches as mpatches
    from PIL import Image, ImageDraw, ImageFont
    
    print("\nGelişmiş TIF oluşturuluyor...")
    
    # TIF verilerini RGB formatına çevir
    if tif_data.shape[0] == 3:  # RGB
        rgb_data = np.transpose(tif_data, (1, 2, 0))
    else:  # Tek band
        # Tek bandı RGB'ye çevir
        single_band = tif_data[0] if len(tif_data.shape) == 3 else tif_data
        rgb_data = np.stack([single_band, single_band, single_band], axis=2)
    
    # Veri tipini uint8'e çevir
    if rgb_data.dtype != np.uint8:
        rgb_data = ((rgb_data - rgb_data.min()) / (rgb_data.max() - rgb_data.min()) * 255).astype(np.uint8)
    
    print(f"RGB veri boyutu: {rgb_data.shape}")
    
    # Matplotlib ile çizim yap
    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    
    # TIF'i arka plan olarak göster
    ax.imshow(rgb_data, extent=[tif_bounds.left, tif_bounds.right, 
                               tif_bounds.bottom, tif_bounds.top], 
              aspect='auto', alpha=0.8)
    
    # Parsel sınırlarını çiz
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'yellow', 'cyan', 'magenta']
    legend_elements = []
    
    for i, (layer_name, gdf) in enumerate(overlapping_features.items()):
        color = colors[i % len(colors)]
        
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            # Polygon sınırlarını çiz
            gdf.boundary.plot(ax=ax, color=color, linewidth=2, alpha=0.9)
            
            # Parsel isimlerini ekle
            for idx, row in gdf.iterrows():
                try:
                    # Polygon'un merkezini bul
                    centroid = row.geometry.centroid
                    
                    # İsmi al
                    name = row.get('Name', f'Parsel {idx}')
                    if name and name != 'Bayburt':  # Genel isim değilse
                        ax.annotate(name, 
                                  (centroid.x, centroid.y),
                                  fontsize=8, 
                                  color='white',
                                  weight='bold',
                                  ha='center', 
                                  va='center',
                                  bbox=dict(boxstyle='round,pad=0.3', 
                                          facecolor=color, 
                                          alpha=0.7,
                                          edgecolor='white'))
                except Exception as e:
                    print(f"İsim ekleme hatası: {e}")
            
            legend_elements.append(mpatches.Patch(color=color, label=f'{layer_name} Sınırları'))
            
        elif 'Point' in gdf.geometry.geom_type.iloc[0]:
            # Noktaları çiz
            gdf.plot(ax=ax, color=color, markersize=50, alpha=0.8, marker='o')
            legend_elements.append(mpatches.Patch(color=color, label=f'{layer_name} Noktaları'))
    
    # Başlık ve legend ekle
    ax.set_title('Parsel Bilgileri ile Geliştirilmiş TIF Görüntüsü', fontsize=16, weight='bold')
    ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
    
    # Koordinat bilgilerini ekle
    ax.set_xlabel('X Koordinatı (m)', fontsize=12)
    ax.set_ylabel('Y Koordinatı (m)', fontsize=12)
    
    # Grid ekle
    ax.grid(True, alpha=0.3)
    
    # Kaydet
    plt.tight_layout()
    plt.savefig(output_path.replace('.tif', '_enhanced.png'), 
                dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Gelişmiş görüntü kaydedildi: {output_path.replace('.tif', '_enhanced.png')}")
    
    # Gerçek TIF dosyası oluştur
    create_georeferenced_tif(rgb_data, tif_transform, tif_bounds, overlapping_features, output_path)
    
    plt.close()

def create_georeferenced_tif(rgb_data, transform, bounds, overlapping_features, output_path):
    """Coğrafi referanslı TIF dosyası oluştur."""
    import rasterio
    from rasterio.features import rasterize
    
    print("Coğrafi referanslı TIF oluşturuluyor...")
    
    # Overlay katmanı oluştur
    overlay = np.zeros((rgb_data.shape[0], rgb_data.shape[1], 4), dtype=np.uint8)  # RGBA
    
    # Parsel sınırlarını overlay'e çiz
    for layer_name, gdf in overlapping_features.items():
        if 'Polygon' in gdf.geometry.geom_type.iloc[0]:
            try:
                # Polygon sınırlarını rasterize et
                shapes = [(geom, 1) for geom in gdf.geometry]
                mask = rasterize(shapes, 
                               out_shape=(rgb_data.shape[0], rgb_data.shape[1]),
                               transform=transform,
                               fill=0,
                               dtype=np.uint8)
                
                # Sınırları overlay'e ekle
                overlay[:, :, 0][mask > 0] = 255  # Kırmızı kanal
                overlay[:, :, 3][mask > 0] = 128  # Alpha kanal
                
            except Exception as e:
                print(f"Rasterize hatası: {e}")
    
    # Ana görüntü ile overlay'i birleştir
    final_image = rgb_data.copy()
    
    # Alpha blending
    alpha = overlay[:, :, 3:4] / 255.0
    final_image = (final_image * (1 - alpha) + overlay[:, :, :3] * alpha).astype(np.uint8)
    
    # TIF olarak kaydet
    height, width = final_image.shape[:2]
    
    with rasterio.open(
        output_path,
        'w',
        driver='GTiff',
        height=height,
        width=width,
        count=3,
        dtype=final_image.dtype,
        crs='EPSG:5257',
        transform=transform,
        compress='lzw'
    ) as dst:
        # RGB bandları yaz
        for i in range(3):
            dst.write(final_image[:, :, i], i + 1)
    
    print(f"Gelişmiş TIF dosyası kaydedildi: {output_path}")

def main():
    """Ana fonksiyon."""
    print("KML Bilgilerini TIF'e İşleme Aracı")
    print("=" * 50)
    
    # Dosya yolları
    tif_path = "demirozu_with_model_output.tif"
    kml_path = "Layers.kml"
    output_path = "demirozu_with_parcels.tif"
    
    # Dosyaların varlığını kontrol et
    if not os.path.exists(tif_path):
        print(f"Hata: {tif_path} dosyası bulunamadı!")
        return
    
    if not os.path.exists(kml_path):
        print(f"Hata: {kml_path} dosyası bulunamadı!")
        return
    
    # Gerekli paketleri kontrol et
    check_and_install_packages()
    
    try:
        # Veriyi yükle
        tif_src, tif_data, tif_transform, kml_gdfs = load_and_prepare_data(tif_path, kml_path)
        
        # Örtüşen özellikleri bul
        overlapping_features = find_overlapping_features(tif_src, kml_gdfs)
        
        # Gelişmiş TIF oluştur
        create_enhanced_tif(tif_data, tif_transform, tif_src.bounds, 
                          overlapping_features, output_path)
        
        print("\n" + "=" * 50)
        print("İşlem tamamlandı!")
        print(f"Yeni TIF dosyası: {output_path}")
        print(f"Görselleştirme: {output_path.replace('.tif', '_enhanced.png')}")
        print("\nArtık TIF dosyasını açtığınızda parsel sınırları görünecek!")
        
    except Exception as e:
        print(f"Hata oluştu: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'tif_src' in locals():
            tif_src.close()

if __name__ == "__main__":
    main()
